'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useSession, signIn, signOut } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { ChevronDown, Zap, User, LogOut, Settings, Sparkles } from 'lucide-react'

export function Header() {
  const { data: session } = useSession()
  const [showToolsMenu, setShowToolsMenu] = useState(false)
  const [showUserMenu, setShowUserMenu] = useState(false)

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
      <div className="container flex h-16 items-center justify-between px-4">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2 group">
          <div className="w-8 h-8 bg-gradient-to-br from-purple-600 to-cyan-500 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
          <span className="text-xl font-bold bg-gradient-to-r from-purple-600 to-cyan-500 bg-clip-text text-transparent">
            ImageUpscaler
          </span>
        </Link>

        {/* Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          {/* Tools Dropdown */}
          <div 
            className="relative"
            onMouseEnter={() => setShowToolsMenu(true)}
            onMouseLeave={() => setShowToolsMenu(false)}
          >
            <button className="flex items-center space-x-1 text-gray-700 hover:text-purple-600 transition-colors duration-200">
              <span>Tools</span>
              <ChevronDown className="w-4 h-4" />
            </button>
            
            {showToolsMenu && (
              <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-xl border border-gray-100 py-2 animate-in slide-in-from-top-2 duration-200">
                <Link href="/" className="flex items-center space-x-3 px-4 py-3 hover:bg-gradient-to-r hover:from-purple-50 hover:to-cyan-50 transition-colors">
                  <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
                    <Sparkles className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">Image Upscaler</div>
                    <div className="text-sm text-gray-500">Enlarge & enhance photos</div>
                  </div>
                </Link>
                <div className="flex items-center space-x-3 px-4 py-3 opacity-50 cursor-not-allowed">
                  <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-teal-500 rounded-lg flex items-center justify-center">
                    <div className="w-4 h-4 bg-white rounded-sm" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">Background Remover</div>
                    <div className="text-sm text-gray-500">Coming soon...</div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <Link href="/pricing" className="text-gray-700 hover:text-purple-600 transition-colors duration-200">
            Pricing
          </Link>
        </nav>

        {/* User State */}
        <div className="flex items-center space-x-4">
          {session ? (
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center space-x-2 bg-gradient-to-r from-purple-100 to-cyan-100 hover:from-purple-200 hover:to-cyan-200 px-3 py-2 rounded-lg transition-all duration-200"
              >
                <Zap className="w-4 h-4 text-purple-600" />
                <span className="font-medium text-purple-700">280</span>
                {session.user?.image ? (
                  <img 
                    src={session.user.image} 
                    alt="Profile" 
                    className="w-6 h-6 rounded-full"
                  />
                ) : (
                  <User className="w-4 h-4 text-purple-600" />
                )}
              </button>

              {showUserMenu && (
                <div className="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-100 py-2 animate-in slide-in-from-top-2 duration-200">
                  <Link href="/dashboard" className="flex items-center space-x-2 px-4 py-2 hover:bg-gray-50 transition-colors">
                    <Settings className="w-4 h-4 text-gray-500" />
                    <span>Dashboard</span>
                  </Link>
                  <button 
                    onClick={() => signOut()}
                    className="flex items-center space-x-2 px-4 py-2 hover:bg-gray-50 transition-colors w-full text-left"
                  >
                    <LogOut className="w-4 h-4 text-gray-500" />
                    <span>Logout</span>
                  </button>
                </div>
              )}
            </div>
          ) : (
            <Button 
              onClick={() => signIn('google')}
              variant="gradient"
              className="shadow-lg hover:shadow-xl"
            >
              Sign In
            </Button>
          )}
        </div>
      </div>
    </header>
  )
}