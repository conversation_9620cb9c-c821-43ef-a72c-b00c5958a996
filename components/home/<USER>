'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Upload, Sparkles, Download } from 'lucide-react'

const steps = [
  {
    icon: Upload,
    title: 'Upload',
    description: 'Drag & drop your images or click to select files. Support for PNG, JPG, and WebP formats.',
    color: 'from-purple-500 to-purple-600'
  },
  {
    icon: Sparkles,
    title: 'Select & Enhance',
    description: 'Choose your desired scale factor and let our AI work its magic to enhance your images.',
    color: 'from-blue-500 to-blue-600'
  },
  {
    icon: Download,
    title: 'Download',
    description: 'Get your high-resolution, enhanced images ready for any use case in seconds.',
    color: 'from-cyan-500 to-cyan-600'
  }
]

export function HowItWorks() {
  return (
    <section className="py-16 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            How It Works
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Transform your images in three simple steps with our AI-powered upscaling technology
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          {steps.map((step, index) => (
            <div key={index} className="text-center group">
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <CardContent className="p-8">
                  <div className={`w-16 h-16 mx-auto mb-6 bg-gradient-to-br ${step.color} rounded-full flex items-center justify-center shadow-lg`}>
                    <step.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {step.description}
                  </p>
                </CardContent>
              </Card>
              
              {index < steps.length - 1 && (
                <div className="hidden md:block absolute top-1/2 right-0 transform translate-x-1/2 -translate-y-1/2">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-200 to-cyan-200 rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-cyan-500 rounded-full" />
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}