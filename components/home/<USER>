'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { ChevronDown, ChevronUp } from 'lucide-react'

const faqs = [
  {
    question: 'How does AI image upscaling work?',
    answer: 'Our AI uses advanced machine learning algorithms trained on millions of images to intelligently predict and add pixels, enhancing image quality while maintaining natural appearance and sharp details.'
  },
  {
    question: 'What image formats are supported?',
    answer: 'We support all major image formats including PNG, JPG, JPEG, and WebP. Maximum file size is 10MB per image for optimal processing speed.'
  },
  {
    question: 'How many credits do I need for upscaling?',
    answer: 'Credit costs vary by scale factor: 2x costs 1 credit, 4x costs 2 credits, 8x costs 4 credits, and 16x costs 8 credits per image.'
  },
  {
    question: 'Can I cancel my subscription anytime?',
    answer: 'Yes! You can cancel your subscription at any time through your dashboard. You\'ll retain access to your plan features until the end of your billing period.'
  },
  {
    question: 'What happens to my images after processing?',
    answer: 'Your images are processed securely and deleted from our servers after 24 hours. We never store or use your images for any other purpose.'
  },
  {
    question: 'Is there a limit on image size?',
    answer: 'Free users can upload images up to 10MB. The output resolution depends on your selected scale factor, with 16x scaling available for Pro and Max users.'
  }
]

export function FAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(null)

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index)
  }

  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Everything you need to know about our AI-powered image upscaling service
          </p>
        </div>

        <div className="max-w-3xl mx-auto space-y-4">
          {faqs.map((faq, index) => (
            <Card key={index} className="border shadow-sm hover:shadow-md transition-shadow duration-200">
              <CardContent className="p-0">
                <button
                  onClick={() => toggleFAQ(index)}
                  className="w-full p-6 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                >
                  <h3 className="text-lg font-semibold text-gray-900 pr-4">
                    {faq.question}
                  </h3>
                  {openIndex === index ? (
                    <ChevronUp className="w-5 h-5 text-purple-600 flex-shrink-0" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-400 flex-shrink-0" />
                  )}
                </button>
                
                {openIndex === index && (
                  <div className="px-6 pb-6 -mt-2">
                    <p className="text-gray-600 leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}