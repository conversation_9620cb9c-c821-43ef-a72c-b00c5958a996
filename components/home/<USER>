import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Sparkles, Scissors, Palette, Wand2 } from 'lucide-react'

const tools = [
  {
    icon: Sparkles,
    name: 'Image Upscaler',
    description: 'Enlarge and enhance your images with AI-powered upscaling up to 16x resolution.',
    status: 'available',
    color: 'from-purple-500 to-blue-500'
  },
  {
    icon: Scissors,
    name: 'Background Remover',
    description: 'Remove backgrounds from images instantly with precision AI technology.',
    status: 'coming-soon',
    color: 'from-green-500 to-teal-500'
  },
  {
    icon: Palette,
    name: 'Color Enhancer',
    description: 'Automatically adjust colors, brightness, and contrast for stunning photos.',
    status: 'coming-soon',
    color: 'from-orange-500 to-red-500'
  },
  {
    icon: Wand2,
    name: 'Style Transfer',
    description: 'Apply artistic styles to your photos with our advanced neural networks.',
    status: 'coming-soon',
    color: 'from-pink-500 to-purple-500'
  }
]

export function ToolsSuite() {
  return (
    <section className="py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Complete Creative Suite
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover our growing collection of AI-powered tools designed to enhance your creative workflow
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {tools.map((tool, index) => (
            <Card 
              key={index} 
              className={`border-0 shadow-lg hover:shadow-xl transition-all duration-300 ${
                tool.status === 'available' ? 'hover:scale-105 cursor-pointer' : 'opacity-75'
              }`}
            >
              <CardContent className="p-6">
                <div className="relative mb-4">
                  <div className={`w-12 h-12 bg-gradient-to-br ${tool.color} rounded-lg flex items-center justify-center shadow-md`}>
                    <tool.icon className="w-6 h-6 text-white" />
                  </div>
                  <Badge 
                    variant={tool.status === 'available' ? 'default' : 'secondary'} 
                    className="absolute -top-2 -right-2 text-xs"
                  >
                    {tool.status === 'available' ? 'Live' : 'Soon'}
                  </Badge>
                </div>
                
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {tool.name}
                </h3>
                <p className="text-sm text-gray-600 leading-relaxed">
                  {tool.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}