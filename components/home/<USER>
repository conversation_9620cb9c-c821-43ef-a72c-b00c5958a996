'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Camera, ShoppingBag, Paintbrush, Home } from 'lucide-react'

const useCases = [
  {
    icon: Camera,
    title: 'Photography',
    description: 'Enhance old photos, increase resolution for prints, and restore image quality.',
    beforeImage: 'https://images.pexels.com/photos/1212487/pexels-photo-1212487.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    afterImage: 'https://images.pexels.com/photos/1212487/pexels-photo-1212487.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    improvement: '4x Resolution'
  },
  {
    icon: ShoppingBag,
    title: 'E-commerce',
    description: 'Create high-quality product images that drive sales and improve customer experience.',
    beforeImage: 'https://images.pexels.com/photos/1464625/pexels-photo-1464625.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    afterImage: 'https://images.pexels.com/photos/1464625/pexels-photo-1464625.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    improvement: 'Sharp Details'
  },
  {
    icon: Paintbrush,
    title: 'Digital Art',
    description: 'Upscale artwork and illustrations while preserving artistic style and details.',
    beforeImage: 'https://images.pexels.com/photos/1183992/pexels-photo-1183992.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    afterImage: 'https://images.pexels.com/photos/1183992/pexels-photo-1183992.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    improvement: 'Crisp Lines'
  },
  {
    icon: Home,
    title: 'Real Estate',
    description: 'Enhance property photos to showcase homes in their best light for listings.',
    beforeImage: 'https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop',
    afterImage: 'https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop',
    improvement: 'HD Quality'
  }
]

export function UseCases() {
  const [activeCase, setActiveCase] = useState(0)

  return (
    <section className="py-16 bg-gradient-to-br from-purple-50 via-blue-50 to-cyan-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Perfect for Every Use Case
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            See how our AI upscaling transforms images across different industries and use cases
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Use Case Cards */}
          <div className="space-y-4">
            {useCases.map((useCase, index) => (
              <Card
                key={index}
                className={`cursor-pointer transition-all duration-300 ${
                  activeCase === index
                    ? 'border-purple-300 shadow-lg bg-gradient-to-r from-purple-50 to-blue-50'
                    : 'hover:shadow-md'
                }`}
                onClick={() => setActiveCase(index)}
              >
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                      activeCase === index
                        ? 'bg-gradient-to-br from-purple-500 to-blue-500'
                        : 'bg-gray-100'
                    }`}>
                      <useCase.icon className={`w-6 h-6 ${
                        activeCase === index ? 'text-white' : 'text-gray-600'
                      }`} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">
                          {useCase.title}
                        </h3>
                        {activeCase === index && (
                          <Badge variant="default" className="bg-gradient-to-r from-purple-500 to-blue-500">
                            {useCase.improvement}
                          </Badge>
                        )}
                      </div>
                      <p className="text-gray-600">
                        {useCase.description}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Before/After Comparison */}
          <div className="relative">
            <Card className="overflow-hidden shadow-xl">
              <div className="grid grid-cols-2">
                <div className="relative">
                  <img
                    src={useCases[activeCase].beforeImage}
                    alt="Before"
                    className="w-full h-64 object-cover"
                  />
                  <Badge className="absolute top-3 left-3 bg-red-500 hover:bg-red-500">
                    Before
                  </Badge>
                </div>
                <div className="relative">
                  <img
                    src={useCases[activeCase].afterImage}
                    alt="After"
                    className="w-full h-64 object-cover"
                  />
                  <Badge className="absolute top-3 right-3 bg-green-500 hover:bg-green-500">
                    After
                  </Badge>
                </div>
              </div>
            </Card>
            
            {/* Divider Line */}
            <div className="absolute inset-y-0 left-1/2 w-1 bg-white transform -translate-x-1/2 shadow-lg rounded-full" />
            <div className="absolute top-1/2 left-1/2 w-8 h-8 bg-white rounded-full shadow-lg transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center">
              <div className="w-3 h-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full" />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}