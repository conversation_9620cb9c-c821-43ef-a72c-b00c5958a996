'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Upload, Image as ImageIcon, Download, Sparkles, Lock, Zap } from 'lucide-react'
import { useSession } from 'next-auth/react'

interface UpscaleResult {
  originalUrl: string
  upscaledUrl: string
  originalSize: { width: number; height: number; fileSize: number }
  upscaledSize: { width: number; height: number; fileSize: number }
}

export function ImageUpscaler() {
  const { data: session } = useSession()
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [scaleFactor, setScaleFactor] = useState<2 | 4 | 8 | 16>(2)
  const [batchMode, setBatchMode] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [results, setResults] = useState<UpscaleResult[]>([])
  const [userPlan] = useState<'free' | 'pro' | 'max'>('free') // TODO: Get from user data

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (batchMode && userPlan === 'free') return // Prevent batch mode for free users
    
    const maxFiles = batchMode ? 5 : 1
    const filesToAdd = acceptedFiles.slice(0, maxFiles)
    setSelectedFiles(filesToAdd)
  }, [batchMode, userPlan])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpg', '.jpeg', '.png', '.webp']
    },
    multiple: batchMode,
    maxSize: 10 * 1024 * 1024 // 10MB
  })

  const getCreditCost = () => {
    const baseCost = scaleFactor === 2 ? 1 : scaleFactor === 4 ? 2 : scaleFactor === 8 ? 4 : 8
    return baseCost * selectedFiles.length
  }

  const canUseScale = (scale: 2 | 4 | 8 | 16) => {
    if (userPlan === 'free') return scale <= 4
    return true
  }

  const handleUpscale = async () => {
    if (!selectedFiles.length || !session) return

    setIsProcessing(true)
    
    try {
      // TODO: Call backend API
      await new Promise(resolve => setTimeout(resolve, 3000)) // Simulate processing
      
      // Mock results
      const mockResults: UpscaleResult[] = selectedFiles.map(file => ({
        originalUrl: URL.createObjectURL(file),
        upscaledUrl: URL.createObjectURL(file), // In real app, this would be the upscaled image
        originalSize: { width: 800, height: 600, fileSize: file.size },
        upscaledSize: { width: 800 * scaleFactor, height: 600 * scaleFactor, fileSize: file.size * 2 }
      }))
      
      setResults(mockResults)
    } catch (error) {
      console.error('Upscale failed:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-8">
      {/* Upload Area */}
      <Card className="border-2 border-dashed border-gray-200 hover:border-purple-300 transition-colors duration-200">
        <CardContent className="p-8">
          <div
            {...getRootProps()}
            className={`relative cursor-pointer transition-all duration-200 ${
              isDragActive ? 'scale-105' : ''
            }`}
          >
            <input {...getInputProps()} />
            <div className="text-center space-y-4">
              <div className="w-16 h-16 mx-auto bg-gradient-to-br from-purple-100 to-cyan-100 rounded-full flex items-center justify-center">
                <Upload className="w-8 h-8 text-purple-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {isDragActive ? 'Drop your images here!' : 'Upload your images'}
                </h3>
                <p className="text-gray-500">
                  Drag & drop or click to select • PNG, JPG, WebP • Max 10MB
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Selected Files */}
      {selectedFiles.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {selectedFiles.map((file, index) => (
            <Card key={index} className="overflow-hidden">
              <div className="aspect-video bg-gray-100 flex items-center justify-center">
                <ImageIcon className="w-8 h-8 text-gray-400" />
              </div>
              <CardContent className="p-4">
                <p className="text-sm font-medium truncate">{file.name}</p>
                <p className="text-xs text-gray-500">
                  {(file.size / 1024 / 1024).toFixed(1)} MB
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Controls */}
      <div className="space-y-6">
        {/* Scale Factor */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Scale Factor
          </label>
          <div className="flex flex-wrap gap-3">
            {([2, 4, 8, 16] as const).map((scale) => (
              <button
                key={scale}
                onClick={() => canUseScale(scale) && setScaleFactor(scale)}
                disabled={!canUseScale(scale)}
                className={`relative px-4 py-2 rounded-lg border-2 transition-all duration-200 ${
                  scaleFactor === scale
                    ? 'border-purple-500 bg-purple-50 text-purple-700'
                    : canUseScale(scale)
                    ? 'border-gray-200 hover:border-purple-300 text-gray-700'
                    : 'border-gray-100 bg-gray-50 text-gray-400 cursor-not-allowed'
                }`}
              >
                {scale}x
                {!canUseScale(scale) && (
                  <Lock className="w-3 h-3 absolute -top-1 -right-1 text-gray-400" />
                )}
              </button>
            ))}
          </div>
          {userPlan === 'free' && (
            <p className="text-xs text-gray-500 mt-2">
              8x and 16x scaling requires Pro or Max plan
            </p>
          )}
        </div>

        {/* Batch Mode */}
        <div className="flex items-center justify-between">
          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={batchMode}
                onChange={(e) => setBatchMode(e.target.checked)}
                disabled={userPlan === 'free'}
                className="w-4 h-4 text-purple-600 rounded border-gray-300 focus:ring-purple-500 disabled:opacity-50"
              />
              <span className="text-sm font-medium text-gray-700">
                Batch Mode (up to 5 images)
              </span>
              {userPlan === 'free' && (
                <Lock className="w-3 h-3 text-gray-400" />
              )}
            </label>
            {userPlan === 'free' && (
              <p className="text-xs text-gray-500 mt-1">
                Batch processing requires Pro or Max plan
              </p>
            )}
          </div>
        </div>

        {/* Process Button */}
        {selectedFiles.length > 0 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <Zap className="w-4 h-4 text-purple-600" />
                <span>Cost: {getCreditCost()} credits</span>
              </div>
            </div>
            <Button
              onClick={handleUpscale}
              disabled={isProcessing || !session}
              variant="gradient"
              size="lg"
              className="min-w-[120px]"
            >
              {isProcessing ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Processing...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Sparkles className="w-4 h-4" />
                  <span>Upscale ({getCreditCost()} Credit{getCreditCost() > 1 ? 's' : ''})</span>
                </div>
              )}
            </Button>
          </div>
        )}
      </div>

      {/* Results */}
      {results.length > 0 && (
        <div className="space-y-6">
          <h3 className="text-xl font-semibold text-gray-900">Results</h3>
          <div className="grid gap-6">
            {results.map((result, index) => (
              <Card key={index} className="p-6">
                <div className="grid md:grid-cols-2 gap-6">
                  {/* Before */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3">Original</h4>
                    <div className="aspect-video bg-gray-100 rounded-lg mb-3 overflow-hidden">
                      <img 
                        src={result.originalUrl} 
                        alt="Original" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>{result.originalSize.width} × {result.originalSize.height}px</p>
                      <p>{(result.originalSize.fileSize / 1024 / 1024).toFixed(1)} MB</p>
                    </div>
                  </div>

                  {/* After */}
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900">Upscaled ({scaleFactor}x)</h4>
                      <Button size="sm" className="gap-2">
                        <Download className="w-4 h-4" />
                        Download
                      </Button>
                    </div>
                    <div className="aspect-video bg-gray-100 rounded-lg mb-3 overflow-hidden">
                      <img 
                        src={result.upscaledUrl} 
                        alt="Upscaled" 
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p className="text-green-600 font-medium">
                        {result.upscaledSize.width} × {result.upscaledSize.height}px
                      </p>
                      <p>{(result.upscaledSize.fileSize / 1024 / 1024).toFixed(1)} MB</p>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}