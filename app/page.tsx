import { ImageUpscaler } from '@/components/tools/ImageUpscaler'
import { HowItWorks } from '@/components/home/<USER>'
import { ToolsSuite } from '@/components/home/<USER>'
import { UseCases } from '@/components/home/<USER>'
import { FAQ } from '@/components/home/<USER>'
import { Button } from '@/components/ui/button'
import { ArrowRight, Sparkles } from 'lucide-react'

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-purple-50 via-blue-50 to-cyan-50 pt-16 pb-24">
        <div className="absolute inset-0 bg-grid-pattern opacity-5" />
        <div className="container mx-auto px-4 relative">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
              AI Image Upscaler:{' '}
              <span className="bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-500 bg-clip-text text-transparent">
                Enlarge & Enhance
              </span>{' '}
              Photos in Seconds
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              Transform your images with cutting-edge AI technology. Upscale photos up to 16x resolution 
              while preserving quality and enhancing details automatically.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button variant="gradient" size="lg" className="gap-2 shadow-lg hover:shadow-xl">
                <Sparkles className="w-5 h-5" />
                Start Upscaling Free
              </Button>
              <Button variant="outline" size="lg" className="gap-2">
                View Pricing
                <ArrowRight className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Embedded Tool */}
          <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-5xl mx-auto">
            <ImageUpscaler />
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <HowItWorks />
      <ToolsSuite />
      <UseCases />
      <FAQ />

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-purple-600 via-blue-600 to-cyan-500">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Transform Your Images?
          </h2>
          <p className="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
            Join thousands of creators, photographers, and businesses who trust our AI to enhance their images.
          </p>
          <Button 
            variant="secondary" 
            size="lg" 
            className="gap-2 bg-white text-purple-600 hover:bg-gray-100 shadow-lg hover:shadow-xl"
          >
            <Sparkles className="w-5 h-5" />
            Get Started Now
          </Button>
        </div>
      </section>
    </div>
  )
}