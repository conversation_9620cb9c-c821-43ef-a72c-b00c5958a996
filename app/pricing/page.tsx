import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON>, <PERSON>rkles, Zap } from 'lucide-react'

const plans = [
  {
    name: 'Free',
    price: '$0',
    period: 'forever',
    credits: 10,
    features: [
      '10 credits per month',
      '2x and 4x upscaling',
      'Single image processing',
      'Standard processing speed',
      'Download in PNG/JPG'
    ],
    limitations: [
      'No batch processing',
      'Limited to 4x upscaling'
    ],
    cta: 'Get Started Free',
    popular: false
  },
  {
    name: 'Pro',
    price: '$4.99',
    period: 'per month',
    credits: 300,
    features: [
      '300 credits per month',
      'All scale factors (2x, 4x, 8x, 16x)',
      'Batch processing (up to 5 images)',
      'Priority processing speed',
      'Download in multiple formats',
      'Email support'
    ],
    limitations: [],
    cta: 'Start Pro Trial',
    popular: true
  },
  {
    name: '<PERSON>',
    price: '$14.99',
    period: 'per month',
    credits: 1500,
    features: [
      '1,500 credits per month',
      'All scale factors (2x, 4x, 8x, 16x)',
      'Batch processing (up to 5 images)',
      'Fastest processing speed',
      'Download in multiple formats',
      'Priority email support',
      'API access (coming soon)'
    ],
    limitations: [],
    cta: 'Start Max Trial',
    popular: false
  }
]

export default function Pricing() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-cyan-50 py-16">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Simple, Transparent{' '}
            <span className="bg-gradient-to-r from-purple-600 to-cyan-500 bg-clip-text text-transparent">
              Pricing
            </span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Choose the perfect plan for your image upscaling needs. All plans include our advanced AI technology.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16">
          {plans.map((plan, index) => (
            <Card 
              key={index} 
              className={`relative border-2 shadow-lg hover:shadow-xl transition-all duration-300 ${
                plan.popular 
                  ? 'border-purple-300 scale-105 shadow-purple-100' 
                  : 'border-gray-200 hover:border-purple-200'
              }`}
            >
              {plan.popular && (
                <Badge className="absolute -top-3 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-purple-600 to-blue-600">
                  Most Popular
                </Badge>
              )}
              
              <CardHeader className="text-center pb-8">
                <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                  {plan.name}
                </CardTitle>
                <div className="flex items-baseline justify-center mb-4">
                  <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                  <span className="text-gray-500 ml-2">/{plan.period}</span>
                </div>
                <div className="flex items-center justify-center space-x-2 text-purple-600">
                  <Zap className="w-5 h-5" />
                  <span className="font-semibold">{plan.credits} credits/month</span>
                </div>
              </CardHeader>

              <CardContent className="space-y-6">
                <ul className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center space-x-3">
                      <Check className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button 
                  variant={plan.popular ? 'gradient' : 'outline'}
                  className="w-full"
                  size="lg"
                >
                  {plan.cta}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Credit Usage Guide */}
        <Card className="max-w-4xl mx-auto mb-16">
          <CardHeader>
            <CardTitle className="text-center text-2xl font-bold text-gray-900">
              How Credits Work
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-4 gap-6 text-center">
              <div className="space-y-3">
                <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-white font-bold text-lg">2x</span>
                </div>
                <h3 className="font-semibold text-gray-900">2x Upscale</h3>
                <p className="text-2xl font-bold text-green-600">1 Credit</p>
              </div>
              <div className="space-y-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-white font-bold text-lg">4x</span>
                </div>
                <h3 className="font-semibold text-gray-900">4x Upscale</h3>
                <p className="text-2xl font-bold text-blue-600">2 Credits</p>
              </div>
              <div className="space-y-3">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-500 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-white font-bold text-lg">8x</span>
                </div>
                <h3 className="font-semibold text-gray-900">8x Upscale</h3>
                <p className="text-2xl font-bold text-purple-600">4 Credits</p>
              </div>
              <div className="space-y-3">
                <div className="w-12 h-12 bg-gradient-to-br from-pink-400 to-pink-500 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-white font-bold text-lg">16x</span>
                </div>
                <h3 className="font-semibold text-gray-900">16x Upscale</h3>
                <p className="text-2xl font-bold text-pink-600">8 Credits</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* FAQ */}
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-8">
            Frequently Asked Questions
          </h2>
          <div className="space-y-6">
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-2">
                  Can I change plans anytime?
                </h3>
                <p className="text-gray-600">
                  Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately, 
                  and we'll prorate your billing accordingly.
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-2">
                  What happens to unused credits?
                </h3>
                <p className="text-gray-600">
                  Credits reset monthly on your billing date and don't roll over. However, you can always 
                  upgrade to get more credits if you need them before your next reset.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <h3 className="font-semibold text-gray-900 mb-2">
                  Is there a free trial?
                </h3>
                <p className="text-gray-600">
                  All new users start with our Free plan, which includes 10 credits to test our service. 
                  Pro and Max plans also come with a 7-day free trial.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}