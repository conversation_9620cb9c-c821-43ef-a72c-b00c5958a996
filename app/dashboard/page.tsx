'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  User, 
  CreditCard, 
  History, 
  Zap, 
  Calendar,
  Download,
  Image as ImageIcon,
  Settings
} from 'lucide-react'

interface UserData {
  plan: 'free' | 'pro' | 'max'
  credits: number
  nextResetDate: string
  jobHistory: Array<{
    id: string
    tool: string
    date: string
    status: 'completed' | 'failed'
    creditCost: number
  }>
}

export default function Dashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [userData, setUserData] = useState<UserData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/')
      return
    }

    if (status === 'authenticated') {
      // Mock data - in real app, fetch from API
      setUserData({
        plan: 'free',
        credits: 8,
        nextResetDate: '2025-02-15',
        jobHistory: [
          {
            id: '1',
            tool: 'Image Upscaler',
            date: '2025-01-15',
            status: 'completed',
            creditCost: 2
          },
          {
            id: '2',
            tool: 'Image Upscaler',
            date: '2025-01-14',
            status: 'completed',
            creditCost: 1
          }
        ]
      })
      setLoading(false)
    }
  }, [status, router])

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'free': return 'bg-gray-100 text-gray-800'
      case 'pro': return 'bg-blue-100 text-blue-800'
      case 'max': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPlanLimits = (plan: string) => {
    switch (plan) {
      case 'free': return { credits: 10, scales: '2x, 4x', batch: false }
      case 'pro': return { credits: 300, scales: 'All scales', batch: true }
      case 'max': return { credentials: 1500, scales: 'All scales', batch: true }
      default: return { credits: 10, scales: '2x, 4x', batch: false }
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="w-8 h-8 border-4 border-purple-600 border-t-transparent rounded-full animate-spin" />
      </div>
    )
  }

  if (!session || !userData) return null

  const planLimits = getPlanLimits(userData.plan)

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
          <p className="text-gray-600">Manage your account and view your usage statistics</p>
        </div>

        {/* Account Overview */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <Card className="md:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Account
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Avatar>
                  <AvatarImage src={session.user?.image || ''} alt="Profile" />
                  <AvatarFallback>
                    {session.user?.name?.charAt(0) || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium text-gray-900">{session.user?.name}</p>
                  <p className="text-sm text-gray-500">{session.user?.email}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5" />
                Credits
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-purple-600 mb-2">
                {userData.credits}
              </div>
              <p className="text-sm text-gray-500 mb-4">
                out of {planLimits.credits} monthly credits
              </p>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(userData.credits / planLimits.credits) * 100}%` }}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Next Reset
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-gray-900 mb-2">
                Feb 15
              </div>
              <p className="text-sm text-gray-500">
                Credits reset monthly on the 15th
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Subscription Status */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="w-5 h-5" />
              Subscription
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Badge className={getPlanColor(userData.plan)}>
                  {userData.plan.toUpperCase()} PLAN
                </Badge>
                <div>
                  <p className="font-medium text-gray-900">
                    {userData.plan === 'free' ? 'Free Plan' : 
                     userData.plan === 'pro' ? 'Pro Plan - $4.99/month' : 
                     'Max Plan - $14.99/month'}
                  </p>
                  <p className="text-sm text-gray-500">
                    {planLimits.scales} • {planLimits.batch ? 'Batch processing' : 'Single images only'}
                  </p>
                </div>
              </div>
              <Button variant="outline" className="gap-2">
                <Settings className="w-4 h-4" />
                Manage Subscription
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Job History */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <History className="w-5 h-5" />
              Recent Jobs
            </CardTitle>
          </CardHeader>
          <CardContent>
            {userData.jobHistory.length > 0 ? (
              <div className="space-y-4">
                {userData.jobHistory.map((job) => (
                  <div key={job.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-purple-100 to-blue-100 rounded-lg flex items-center justify-center">
                        <ImageIcon className="w-5 h-5 text-purple-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{job.tool}</p>
                        <p className="text-sm text-gray-500">{job.date}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Badge variant={job.status === 'completed' ? 'default' : 'destructive'}>
                        {job.status}
                      </Badge>
                      <span className="text-sm text-gray-500">
                        {job.creditCost} credit{job.creditCost > 1 ? 's' : ''}
                      </span>
                      {job.status === 'completed' && (
                        <Button size="sm" variant="outline" className="gap-1">
                          <Download className="w-3 h-3" />
                          Download
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <ImageIcon className="w-12 h-12 mx-auto mb-4 opacity-30" />
                <p>No jobs yet. Start by uploading an image!</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}