export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          stripe_customer_id: string | null
          created_at: string
        }
        Insert: {
          id?: string
          email: string
          stripe_customer_id?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          email?: string
          stripe_customer_id?: string | null
          created_at?: string
        }
      }
      plans: {
        Row: {
          id: string
          name: string
          price_id: string
          monthly_credits: number
        }
        Insert: {
          id: string
          name: string
          price_id: string
          monthly_credits: number
        }
        Update: {
          id?: string
          name?: string
          price_id?: string
          monthly_credits?: number
        }
      }
      subscriptions: {
        Row: {
          id: string
          user_id: string
          plan_id: string
          stripe_subscription_id: string | null
          status: string
          current_period_end: string
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          plan_id: string
          stripe_subscription_id?: string | null
          status: string
          current_period_end: string
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          plan_id?: string
          stripe_subscription_id?: string | null
          status?: string
          current_period_end?: string
          created_at?: string
        }
      }
      credit_transactions: {
        Row: {
          id: string
          user_id: string
          amount: number
          source_type: string
          source_id: string | null
          description: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          amount: number
          source_type: string
          source_id?: string | null
          description?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          amount?: number
          source_type?: string
          source_id?: string | null
          description?: string | null
          created_at?: string
        }
      }
      jobs: {
        Row: {
          id: string
          user_id: string
          tool_type: string
          status: string
          credit_cost: number
          job_details: any
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          tool_type: string
          status: string
          credit_cost: number
          job_details?: any
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          tool_type?: string
          status?: string
          credit_cost?: number
          job_details?: any
          created_at?: string
        }
      }
    }
  }
}