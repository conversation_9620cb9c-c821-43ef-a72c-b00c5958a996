import { NextAuthOptions } from 'next-auth'
import GoogleProvider from 'next-auth/providers/google'
import { createServerClient } from './supabase'

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      if (account?.provider === 'google' && user.email) {
        const supabase = createServerClient()
        
        // Check if user exists
        const { data: existingUser } = await supabase
          .from('users')
          .select('*')
          .eq('email', user.email)
          .single()

        if (!existingUser) {
          // Create new user
          const { error } = await supabase
            .from('users')
            .insert([{ email: user.email }])

          if (error) {
            console.error('Error creating user:', error)
            return false
          }

          // Get the created user
          const { data: newUser } = await supabase
            .from('users')
            .select('*')
            .eq('email', user.email)
            .single()

          if (newUser) {
            // Create free subscription
            await supabase
              .from('subscriptions')
              .insert([{
                user_id: newUser.id,
                plan_id: 'free',
                status: 'active',
                current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
              }])

            // Grant initial free credits
            await supabase
              .from('credit_transactions')
              .insert([{
                user_id: newUser.id,
                amount: 10,
                source_type: 'INITIAL_GRANT',
                description: 'Welcome bonus - Free plan credits'
              }])
          }
        }
      }
      return true
    },
    async session({ session, token }) {
      if (session.user?.email) {
        const supabase = createServerClient()
        const { data: user } = await supabase
          .from('users')
          .select('id')
          .eq('email', session.user.email)
          .single()

        if (user) {
          session.user.id = user.id
        }
      }
      return session
    },
  },
  pages: {
    signIn: '/auth/signin',
  },
}