import { NextApiRequest, NextApiResponse } from 'next'
import { createServerClient } from '@/lib/supabase'
import Stripe from 'stripe'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
})

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  const sig = req.headers['stripe-signature']!
  let event: Stripe.Event

  try {
    const body = JSON.stringify(req.body)
    event = stripe.webhooks.constructEvent(body, sig, endpointSecret)
  } catch (err: any) {
    console.error('Webhook signature verification failed:', err.message)
    return res.status(400).json({ message: 'Webhook signature verification failed' })
  }

  const supabase = createServerClient()

  try {
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session
        const customerId = session.customer as string
        const subscriptionId = session.subscription as string

        // Get customer email from session
        const customerEmail = session.customer_details?.email
        if (!customerEmail) break

        // Find user by email
        const { data: user } = await supabase
          .from('users')
          .select('*')
          .eq('email', customerEmail)
          .single()

        if (!user) break

        // Update user with Stripe customer ID
        await supabase
          .from('users')
          .update({ stripe_customer_id: customerId })
          .eq('id', user.id)

        // Get subscription details from Stripe
        const subscription = await stripe.subscriptions.retrieve(subscriptionId)
        const priceId = subscription.items.data[0].price.id

        // Find plan by price ID
        const { data: plan } = await supabase
          .from('plans')
          .select('*')
          .eq('price_id', priceId)
          .single()

        if (!plan) break

        // Create subscription record
        await supabase
          .from('subscriptions')
          .upsert([{
            user_id: user.id,
            plan_id: plan.id,
            stripe_subscription_id: subscriptionId,
            status: subscription.status,
            current_period_end: new Date(subscription.current_period_end * 1000).toISOString()
          }])

        // Grant initial credits
        await supabase
          .from('credit_transactions')
          .insert([{
            user_id: user.id,
            amount: plan.monthly_credits,
            source_type: 'SUBSCRIPTION_START',
            description: `Initial credits for ${plan.name}`
          }])

        break
      }

      case 'invoice.paid': {
        const invoice = event.data.object as Stripe.Invoice
        const subscriptionId = invoice.subscription as string

        // Find subscription
        const { data: subscription } = await supabase
          .from('subscriptions')
          .select('*, plans(*)')
          .eq('stripe_subscription_id', subscriptionId)
          .single()

        if (!subscription) break

        // Grant monthly credits
        await supabase
          .from('credit_transactions')
          .insert([{
            user_id: subscription.user_id,
            amount: subscription.plans.monthly_credits,
            source_type: 'SUBSCRIPTION_RENEWAL',
            description: `Monthly credits renewal for ${subscription.plans.name}`
          }])

        break
      }

      case 'customer.subscription.updated': {
        const stripeSubscription = event.data.object as Stripe.Subscription
        const priceId = stripeSubscription.items.data[0].price.id

        // Find plan by price ID
        const { data: plan } = await supabase
          .from('plans')
          .select('*')
          .eq('price_id', priceId)
          .single()

        if (!plan) break

        // Update subscription
        await supabase
          .from('subscriptions')
          .update({
            plan_id: plan.id,
            status: stripeSubscription.status,
            current_period_end: new Date(stripeSubscription.current_period_end * 1000).toISOString()
          })
          .eq('stripe_subscription_id', stripeSubscription.id)

        break
      }

      case 'customer.subscription.deleted': {
        const stripeSubscription = event.data.object as Stripe.Subscription

        // Find subscription and user
        const { data: subscription } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('stripe_subscription_id', stripeSubscription.id)
          .single()

        if (!subscription) break

        // Update to free plan
        await supabase
          .from('subscriptions')
          .update({
            plan_id: 'free',
            status: 'canceled',
            stripe_subscription_id: null,
            current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
          })
          .eq('id', subscription.id)

        break
      }
    }

    res.status(200).json({ received: true })
  } catch (error) {
    console.error('Webhook processing error:', error)
    res.status(500).json({ message: 'Webhook processing failed' })
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}